# 导入相关包
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import scanpy as sc
import scipy.stats as ss
import seaborn as sns
from tqdm import tqdm

import torch
import torch_geometric
from torch_geometric.loader import DataLoader

# 导入celcomen模块
from celcomen.datareaders.datareader import get_dataset_loaders
from celcomen.models.celcomen import celcomen
from celcomen.models.simcomen import simcomen
from celcomen.training_plan.train import train
from celcomen.utils.helpers import calc_gex, normalize_g2g, calc_sphex

# 设置图形参数
sc.settings.set_figure_params(dpi=100)

# 设置随机种子以确保结果可重现
np.random.seed(42)
torch.manual_seed(42)

print("所有包导入成功！")
print(f"PyTorch版本: {torch.__version__}")
print(f"PyTorch Geometric版本: {torch_geometric.__version__}")
print(f"Scanpy版本: {sc.__version__}")

# 加载数据
# 注意：请确保您已经下载了数据并放在正确的目录中
data_path = "data/xenium_human_glioblastoma.h5ad"  # 根据实际数据路径调整

try:
    # 读取AnnData对象
    adata = sc.read_h5ad(data_path)
    print(f"数据加载成功！")
    print(f"数据形状: {adata.shape}")
    print(f"细胞数量: {adata.n_obs}")
    print(f"基因数量: {adata.n_vars}")
except FileNotFoundError:
    print(f"未找到数据文件: {data_path}")
    print("请确保您已经下载了数据并放在正确的目录中")
    # 创建示例数据用于演示
    print("创建示例数据用于演示...")
    n_cells = 1000
    n_genes = 500
    
    # 生成随机基因表达数据
    X = np.random.poisson(5, size=(n_cells, n_genes))
    
    # 生成空间坐标
    spatial_coords = np.random.uniform(0, 100, size=(n_cells, 2))
    
    # 创建AnnData对象
    adata = sc.AnnData(X=X)
    adata.obsm['spatial'] = spatial_coords
    adata.obs['sample_id'] = 'sample_1'
    
    # 添加基因名称
    adata.var_names = [f'Gene_{i}' for i in range(n_genes)]
    adata.obs_names = [f'Cell_{i}' for i in range(n_cells)]
    
    print(f"示例数据创建完成！")
    print(f"数据形状: {adata.shape}")

# 数据探索
print("=== 数据基本信息 ===")
print(f"观测值（细胞）数量: {adata.n_obs}")
print(f"变量（基因）数量: {adata.n_vars}")
print(f"数据类型: {type(adata.X)}")

# 检查空间坐标
if 'spatial' in adata.obsm.keys():
    print(f"空间坐标形状: {adata.obsm['spatial'].shape}")
    print(f"空间坐标范围: X轴 [{adata.obsm['spatial'][:, 0].min():.2f}, {adata.obsm['spatial'][:, 0].max():.2f}]")
    print(f"                Y轴 [{adata.obsm['spatial'][:, 1].min():.2f}, {adata.obsm['spatial'][:, 1].max():.2f}]")
else:
    print("警告：未找到空间坐标信息")

# 检查样本ID
if 'sample_id' in adata.obs.columns:
    print(f"样本数量: {adata.obs['sample_id'].nunique()}")
    print(f"样本ID: {adata.obs['sample_id'].unique()}")
else:
    print("添加样本ID...")
    adata.obs['sample_id'] = 'sample_1'

# 基因表达统计
print("\n=== 基因表达统计 ===")
print(f"总计数: {adata.X.sum():.0f}")
print(f"每个细胞的平均基因数: {(adata.X > 0).sum(axis=1).mean():.1f}")
print(f"每个基因的平均细胞数: {(adata.X > 0).sum(axis=0).mean():.1f}")

# 可视化空间分布
fig, axes = plt.subplots(1, 2, figsize=(15, 6))

# 空间坐标散点图
axes[0].scatter(adata.obsm['spatial'][:, 0], adata.obsm['spatial'][:, 1], 
                alpha=0.6, s=1, c='blue')
axes[0].set_xlabel('X坐标')
axes[0].set_ylabel('Y坐标')
axes[0].set_title('细胞空间分布')
axes[0].grid(True, alpha=0.3)

# 总基因表达量的空间分布
total_counts = np.array(adata.X.sum(axis=1)).flatten()
scatter = axes[1].scatter(adata.obsm['spatial'][:, 0], adata.obsm['spatial'][:, 1], 
                         c=total_counts, alpha=0.6, s=1, cmap='viridis')
axes[1].set_xlabel('X坐标')
axes[1].set_ylabel('Y坐标')
axes[1].set_title('总基因表达量的空间分布')
plt.colorbar(scatter, ax=axes[1], label='总计数')

plt.tight_layout()
plt.show()

print(f"空间分布可视化完成")

# 数据质量控制
print("=== 数据质量控制 ===")

# 计算质量指标
# 每个细胞的基因数
adata.obs['n_genes'] = (adata.X > 0).sum(axis=1)
# 每个细胞的总计数
adata.obs['total_counts'] = np.array(adata.X.sum(axis=1)).flatten()
# 每个基因的细胞数
adata.var['n_cells'] = (adata.X > 0).sum(axis=0)

print(f"过滤前 - 细胞数: {adata.n_obs}, 基因数: {adata.n_vars}")

# 过滤低质量细胞
# 过滤表达基因数太少的细胞
min_genes = 50
sc.pp.filter_cells(adata, min_genes=min_genes)
print(f"过滤表达基因数<{min_genes}的细胞后 - 细胞数: {adata.n_obs}")

# 过滤在太少细胞中表达的基因
min_cells = 10
sc.pp.filter_genes(adata, min_cells=min_cells)
print(f"过滤在<{min_cells}个细胞中表达的基因后 - 基因数: {adata.n_vars}")

# 选择高变基因（可选）
if adata.n_vars > 2000:
    print("选择高变基因...")
    # 计算高变基因
    sc.pp.highly_variable_genes(adata, n_top_genes=2000, flavor='seurat_v3')
    # 保留高变基因
    adata = adata[:, adata.var.highly_variable]
    print(f"选择高变基因后 - 基因数: {adata.n_vars}")

print(f"\n最终数据形状: {adata.shape}")
print(f"数据类型: {adata.X.dtype}")

# 确保数据是整数类型（原始计数）
if not np.issubdtype(adata.X.dtype, np.integer):
    print("将数据转换为整数类型...")
    adata.X = adata.X.astype(np.int32)

print("数据质量控制完成！")

# 保存处理后的数据
processed_data_path = "processed_data.h5ad"
adata.write(processed_data_path)
print(f"处理后的数据已保存到: {processed_data_path}")

# 设置参数
n_neighbors = 6  # 空间邻居数量（适用于Visium数据）
distance = 50.0  # 邻居距离阈值
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"使用设备: {device}")

# 创建数据加载器
print("创建数据加载器...")
try:
    loader = get_dataset_loaders(
        h5ad_path=processed_data_path,
        sample_id_name='sample_id',
        n_neighbors=n_neighbors,
        distance=distance,
        device=device,
        verbose=True
    )
    print("数据加载器创建成功！")
    print(f"批次数量: {len(loader)}")
    
    # 检查第一个批次
    for batch in loader:
        print(f"批次形状: {batch.x.shape}")
        print(f"边索引形状: {batch.edge_index.shape}")
        print(f"批次索引形状: {batch.batch.shape}")
        break
        
except Exception as e:
    print(f"创建数据加载器时出错: {e}")
    print("使用简化的数据加载器...")
    
    # 创建简化的数据加载器
    from sklearn.neighbors import kneighbors_graph
    import torch_geometric.data as data
    
    # 构建k近邻图
    spatial_coords = adata.obsm['spatial']
    knn_graph = kneighbors_graph(spatial_coords, n_neighbors=n_neighbors, mode='connectivity')
    
    # 转换为PyTorch Geometric格式
    edge_index = torch.tensor(np.array(knn_graph.nonzero()), dtype=torch.long)
    x = torch.tensor(adata.X.toarray() if hasattr(adata.X, 'toarray') else adata.X, dtype=torch.float32)
    
    # 创建数据对象
    graph_data = data.Data(x=x, edge_index=edge_index)
    loader = data.DataLoader([graph_data], batch_size=1, shuffle=False)
    
    print("简化数据加载器创建成功！")

# 模型参数
input_dim = adata.n_vars  # 输入维度（基因数量）
output_dim = min(100, input_dim // 2)  # 输出维度
learning_rate = 0.01
epochs = 100
zmft_scalar = 1e-1  # 平均场理论标量
seed = 42

print(f"模型参数:")
print(f"  输入维度: {input_dim}")
print(f"  输出维度: {output_dim}")
print(f"  学习率: {learning_rate}")
print(f"  训练轮数: {epochs}")
print(f"  设备: {device}")

# 初始化模型
model = celcomen(
    input_dim=input_dim,
    output_dim=output_dim,
    n_neighbors=n_neighbors,
    seed=seed
).to(device)

print(f"\n模型初始化完成！")
print(f"模型参数数量: {sum(p.numel() for p in model.parameters())}")
print(f"可训练参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")

# 显示模型结构
print(f"\n模型结构:")
print(model)

# 训练模型
print("开始训练模型...")
print(f"训练设备: {device}")

try:
    # 训练模型
    losses = train(
        epochs=epochs,
        learning_rate=learning_rate,
        model=model,
        loader=loader,
        zmft_scalar=zmft_scalar,
        seed=seed,
        device=device,
        verbose=True
    )
    
    print(f"\n训练完成！")
    print(f"最终损失: {losses[-1]:.6f}")
    
    # 绘制训练损失曲线
    plt.figure(figsize=(10, 6))
    plt.plot(losses)
    plt.xlabel('训练轮数')
    plt.ylabel('损失值')
    plt.title('训练损失曲线')
    plt.grid(True, alpha=0.3)
    plt.show()
    
except Exception as e:
    print(f"训练过程中出错: {e}")
    print("尝试简化训练...")
    
    # 简化训练过程
    optimizer = torch.optim.SGD(model.parameters(), lr=learning_rate)
    model.train()
    
    losses = []
    for epoch in tqdm(range(epochs)):
        epoch_losses = []
        for batch in loader:
            batch = batch.to(device)
            optimizer.zero_grad()
            
            # 前向传播
            try:
                msg, msg_intra, log_z_mft = model(batch.edge_index, batch.batch)
                
                # 计算损失
                loss = -log_z_mft.mean()  # 简化的损失函数
                
                # 反向传播
                loss.backward()
                optimizer.step()
                
                epoch_losses.append(loss.item())
            except Exception as batch_error:
                print(f"批次处理错误: {batch_error}")
                continue
        
        if epoch_losses:
            avg_loss = np.mean(epoch_losses)
            losses.append(avg_loss)
            
            if epoch % 10 == 0:
                print(f"轮数 {epoch}, 损失: {avg_loss:.6f}")
    
    print("简化训练完成！")

# 提取学习到的网络
print("提取学习到的基因网络...")

model.eval()
with torch.no_grad():
    # 提取基因间相互作用矩阵（G2G）
    g2g_matrix = model.conv1.lin.weight.detach().cpu().numpy()
    print(f"基因间相互作用矩阵形状: {g2g_matrix.shape}")
    
    # 提取细胞内调控矩阵
    intra_matrix = model.lin.weight.detach().cpu().numpy()
    print(f"细胞内调控矩阵形状: {intra_matrix.shape}")

# 可视化网络
fig, axes = plt.subplots(1, 2, figsize=(15, 6))

# 基因间相互作用热图
im1 = axes[0].imshow(g2g_matrix, cmap='RdBu_r', aspect='auto')
axes[0].set_title('基因间相互作用矩阵 (G2G)')
axes[0].set_xlabel('输入基因')
axes[0].set_ylabel('输出特征')
plt.colorbar(im1, ax=axes[0])

# 细胞内调控热图
im2 = axes[1].imshow(intra_matrix, cmap='RdBu_r', aspect='auto')
axes[1].set_title('细胞内调控矩阵')
axes[1].set_xlabel('输入基因')
axes[1].set_ylabel('输出特征')
plt.colorbar(im2, ax=axes[1])

plt.tight_layout()
plt.show()

# 网络统计
print(f"\n网络统计:")
print(f"G2G矩阵 - 平均值: {g2g_matrix.mean():.6f}, 标准差: {g2g_matrix.std():.6f}")
print(f"细胞内矩阵 - 平均值: {intra_matrix.mean():.6f}, 标准差: {intra_matrix.std():.6f}")

# 初始化SimComen模型用于反事实分析
print("初始化SimComen模型用于反事实分析...")

# 创建SimComen模型
sim_model = simcomen(
    input_dim=input_dim,
    output_dim=output_dim,
    n_neighbors=n_neighbors,
    seed=seed
).to(device)

# 将训练好的权重转移到SimComen模型
sim_model.conv1.lin.weight = model.conv1.lin.weight
sim_model.conv1.bias = model.conv1.bias
sim_model.lin.weight = model.lin.weight
sim_model.lin.bias = model.lin.bias

print("SimComen模型初始化完成！")

# 设置原始基因表达数据
original_gex = torch.tensor(adata.X.toarray() if hasattr(adata.X, 'toarray') else adata.X, 
                           dtype=torch.float32).to(device)

print(f"原始基因表达数据形状: {original_gex.shape}")
print(f"基因表达范围: [{original_gex.min():.2f}, {original_gex.max():.2f}]")

# 基因敲除模拟
print("进行基因敲除模拟...")

# 选择要敲除的基因（选择表达量较高的基因）
gene_expression_mean = original_gex.mean(dim=0)
top_genes_idx = torch.argsort(gene_expression_mean, descending=True)[:10]
target_gene_idx = top_genes_idx[0].item()  # 选择表达量最高的基因

print(f"选择敲除基因索引: {target_gene_idx}")
print(f"该基因的平均表达量: {gene_expression_mean[target_gene_idx]:.2f}")

# 创建敲除后的基因表达数据
knockout_gex = original_gex.clone()
knockout_gex[:, target_gene_idx] = 0  # 将目标基因表达设为0

print(f"敲除后基因表达数据创建完成")
print(f"敲除基因的表达量: {knockout_gex[:, target_gene_idx].sum():.2f}")

# 计算表达差异
expression_diff = knockout_gex - original_gex
print(f"表达差异统计:")
print(f"  平均差异: {expression_diff.mean():.6f}")
print(f"  最大差异: {expression_diff.max():.6f}")
print(f"  最小差异: {expression_diff.min():.6f}")

# 预测敲除效果
print("预测基因敲除的空间效果...")

sim_model.eval()
with torch.no_grad():
    try:
        # 设置原始和敲除后的基因表达
        sim_model.set_gex(original_gex)
        
        # 获取第一个批次进行预测
        for batch in loader:
            batch = batch.to(device)
            
            # 原始预测
            original_msg, original_msg_intra, original_log_z = sim_model(batch.edge_index, batch.batch)
            
            # 敲除后预测
            sim_model.set_gex(knockout_gex)
            knockout_msg, knockout_msg_intra, knockout_log_z = sim_model(batch.edge_index, batch.batch)
            
            break  # 只处理第一个批次
        
        # 计算预测差异
        msg_diff = knockout_msg - original_msg
        msg_intra_diff = knockout_msg_intra - original_msg_intra
        log_z_diff = knockout_log_z - original_log_z
        
        print("预测完成！")
        print(f"细胞间信息差异统计:")
        print(f"  平均差异: {msg_diff.mean():.6f}")
        print(f"  标准差: {msg_diff.std():.6f}")
        
        print(f"细胞内信息差异统计:")
        print(f"  平均差异: {msg_intra_diff.mean():.6f}")
        print(f"  标准差: {msg_intra_diff.std():.6f}")
        
        print(f"配分函数差异: {log_z_diff.mean():.6f}")
        
    except Exception as e:
        print(f"预测过程中出错: {e}")
        print("使用简化预测方法...")
        
        # 简化预测：直接比较基因表达
        msg_diff = knockout_gex - original_gex
        print(f"简化预测完成，表达差异范围: [{msg_diff.min():.2f}, {msg_diff.max():.2f}]")

# 可视化敲除效果
print("可视化基因敲除的空间效果...")

# 计算每个细胞的总效应
if 'msg_diff' in locals():
    if msg_diff.dim() > 1:
        total_effect = msg_diff.sum(dim=1).cpu().numpy()
    else:
        total_effect = msg_diff.cpu().numpy()
else:
    total_effect = expression_diff.sum(dim=1).cpu().numpy()

# 创建可视化
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# 原始基因表达的空间分布
original_target_expr = original_gex[:, target_gene_idx].cpu().numpy()
scatter1 = axes[0, 0].scatter(adata.obsm['spatial'][:, 0], adata.obsm['spatial'][:, 1], 
                             c=original_target_expr, alpha=0.6, s=1, cmap='viridis')
axes[0, 0].set_title(f'原始基因表达 (基因 {target_gene_idx})')
axes[0, 0].set_xlabel('X坐标')
axes[0, 0].set_ylabel('Y坐标')
plt.colorbar(scatter1, ax=axes[0, 0])

# 敲除后基因表达的空间分布
knockout_target_expr = knockout_gex[:, target_gene_idx].cpu().numpy()
scatter2 = axes[0, 1].scatter(adata.obsm['spatial'][:, 0], adata.obsm['spatial'][:, 1], 
                             c=knockout_target_expr, alpha=0.6, s=1, cmap='viridis')
axes[0, 1].set_title(f'敲除后基因表达 (基因 {target_gene_idx})')
axes[0, 1].set_xlabel('X坐标')
axes[0, 1].set_ylabel('Y坐标')
plt.colorbar(scatter2, ax=axes[0, 1])

# 总效应的空间分布
scatter3 = axes[1, 0].scatter(adata.obsm['spatial'][:, 0], adata.obsm['spatial'][:, 1], 
                             c=total_effect, alpha=0.6, s=1, cmap='RdBu_r')
axes[1, 0].set_title('基因敲除的总效应')
axes[1, 0].set_xlabel('X坐标')
axes[1, 0].set_ylabel('Y坐标')
plt.colorbar(scatter3, ax=axes[1, 0])

# 效应分布直方图
axes[1, 1].hist(total_effect, bins=50, alpha=0.7, edgecolor='black')
axes[1, 1].set_title('总效应分布')
axes[1, 1].set_xlabel('效应大小')
axes[1, 1].set_ylabel('细胞数量')
axes[1, 1].axvline(x=0, color='red', linestyle='--', alpha=0.7, label='无效应')
axes[1, 1].legend()

plt.tight_layout()
plt.show()

print(f"可视化完成！")
print(f"受影响的细胞数量: {np.sum(np.abs(total_effect) > 0.1)}")
print(f"平均效应大小: {np.mean(np.abs(total_effect)):.6f}")

# 分析基因网络
print("=== 基因网络分析 ===")

# 分析基因间相互作用强度
g2g_abs = np.abs(g2g_matrix)
strong_interactions = g2g_abs > np.percentile(g2g_abs, 95)
print(f"强相互作用数量: {strong_interactions.sum()}")
print(f"强相互作用比例: {strong_interactions.sum() / g2g_abs.size * 100:.2f}%")

# 分析细胞内调控
intra_abs = np.abs(intra_matrix)
strong_intra = intra_abs > np.percentile(intra_abs, 95)
print(f"强细胞内调控数量: {strong_intra.sum()}")
print(f"强细胞内调控比例: {strong_intra.sum() / intra_abs.size * 100:.2f}%")

# 网络连通性分析
print(f"\n=== 网络连通性 ===")
print(f"G2G矩阵非零元素比例: {(g2g_abs > 1e-6).sum() / g2g_abs.size * 100:.2f}%")
print(f"细胞内矩阵非零元素比例: {(intra_abs > 1e-6).sum() / intra_abs.size * 100:.2f}%")

# 基因重要性排序
gene_importance = g2g_abs.sum(axis=0) + intra_abs.sum(axis=0)
top_genes = np.argsort(gene_importance)[-10:][::-1]

print(f"\n=== 重要基因排序（前10） ===")
for i, gene_idx in enumerate(top_genes):
    gene_name = adata.var_names[gene_idx] if hasattr(adata, 'var_names') else f"Gene_{gene_idx}"
    print(f"{i+1:2d}. {gene_name} (索引: {gene_idx}, 重要性: {gene_importance[gene_idx]:.4f})")

# 空间效应分析
print("=== 空间效应分析 ===")

# 计算空间自相关性
from scipy.spatial.distance import pdist, squareform
from scipy.stats import pearsonr

# 计算空间距离矩阵
spatial_coords = adata.obsm['spatial']
spatial_dist = squareform(pdist(spatial_coords))

# 计算效应的空间自相关
if len(total_effect) > 1:
    # 选择距离阈值
    distance_threshold = np.percentile(spatial_dist[spatial_dist > 0], 10)
    
    # 找到邻近细胞对
    neighbor_pairs = np.where((spatial_dist > 0) & (spatial_dist < distance_threshold))
    
    if len(neighbor_pairs[0]) > 0:
        # 计算邻近细胞间效应的相关性
        neighbor_effects_1 = total_effect[neighbor_pairs[0]]
        neighbor_effects_2 = total_effect[neighbor_pairs[1]]
        
        if len(neighbor_effects_1) > 1:
            spatial_corr, p_value = pearsonr(neighbor_effects_1, neighbor_effects_2)
            print(f"空间自相关系数: {spatial_corr:.4f} (p值: {p_value:.4f})")
        else:
            print("邻近细胞对数量不足，无法计算空间自相关")
    else:
        print("未找到邻近细胞对")
else:
    print("效应数据不足，无法进行空间分析")

# 效应强度分布分析
print(f"\n=== 效应强度分析 ===")
effect_abs = np.abs(total_effect)
print(f"效应强度统计:")
print(f"  平均值: {effect_abs.mean():.6f}")
print(f"  中位数: {np.median(effect_abs):.6f}")
print(f"  标准差: {effect_abs.std():.6f}")
print(f"  最大值: {effect_abs.max():.6f}")

# 效应方向分析
positive_effects = np.sum(total_effect > 0)
negative_effects = np.sum(total_effect < 0)
no_effects = np.sum(np.abs(total_effect) < 1e-6)

print(f"\n=== 效应方向分析 ===")
print(f"正效应细胞数: {positive_effects} ({positive_effects/len(total_effect)*100:.1f}%)")
print(f"负效应细胞数: {negative_effects} ({negative_effects/len(total_effect)*100:.1f}%)")
print(f"无效应细胞数: {no_effects} ({no_effects/len(total_effect)*100:.1f}%)")

# 保存结果
print("=== 保存分析结果 ===")

# 保存模型
model_save_path = "celcomen_model.pth"
torch.save({
    'model_state_dict': model.state_dict(),
    'model_params': {
        'input_dim': input_dim,
        'output_dim': output_dim,
        'n_neighbors': n_neighbors,
        'seed': seed
    },
    'training_params': {
        'learning_rate': learning_rate,
        'epochs': epochs,
        'zmft_scalar': zmft_scalar
    },
    'losses': losses if 'losses' in locals() else []
}, model_save_path)
print(f"模型已保存到: {model_save_path}")

# 保存网络矩阵
np.save("g2g_matrix.npy", g2g_matrix)
np.save("intra_matrix.npy", intra_matrix)
print("网络矩阵已保存")

# 保存效应结果
results_dict = {
    'target_gene_idx': target_gene_idx,
    'total_effect': total_effect,
    'spatial_coords': spatial_coords,
    'gene_importance': gene_importance,
    'top_genes': top_genes
}
np.save("knockout_results.npy", results_dict)
print("敲除结果已保存")

# 创建结果摘要
summary = f"""
=== Celcomen 分析结果摘要 ===

数据信息:
- 细胞数量: {adata.n_obs}
- 基因数量: {adata.n_vars}
- 空间坐标范围: X[{spatial_coords[:, 0].min():.1f}, {spatial_coords[:, 0].max():.1f}], Y[{spatial_coords[:, 1].min():.1f}, {spatial_coords[:, 1].max():.1f}]

模型参数:
- 输入维度: {input_dim}
- 输出维度: {output_dim}
- 邻居数量: {n_neighbors}
- 训练轮数: {epochs}
- 学习率: {learning_rate}

网络分析:
- 强基因间相互作用: {strong_interactions.sum()} ({strong_interactions.sum() / g2g_abs.size * 100:.2f}%)
- 强细胞内调控: {strong_intra.sum()} ({strong_intra.sum() / intra_abs.size * 100:.2f}%)

基因敲除分析:
- 敲除基因索引: {target_gene_idx}
- 受影响细胞数: {np.sum(np.abs(total_effect) > 0.1)}
- 平均效应强度: {np.mean(np.abs(total_effect)):.6f}
- 正效应细胞: {positive_effects} ({positive_effects/len(total_effect)*100:.1f}%)
- 负效应细胞: {negative_effects} ({negative_effects/len(total_effect)*100:.1f}%)

文件输出:
- 模型文件: {model_save_path}
- 网络矩阵: g2g_matrix.npy, intra_matrix.npy
- 敲除结果: knockout_results.npy
"""

print(summary)

# 保存摘要到文件
with open("analysis_summary.txt", "w", encoding="utf-8") as f:
    f.write(summary)

print("\n分析完成！所有结果已保存。")