{"cells": [{"cell_type": "markdown", "id": "title-cell", "metadata": {}, "source": ["# Celcomen: 空间转录组学中的细胞通讯能量分析\n", "\n", "## 项目简介\n", "\n", "基因组学和机器学习的进步彻底改变了这两个领域，例如催生了虚拟细胞模型，这些模型可以预测细胞微环境和宏环境的变化（如供体年龄的扰动、细胞所在组织、药物治疗、引导RNA的敲除等）对基因表达的影响。\n", "\n", "相反，虚拟组织模型不仅旨在估计环境对细胞的影响，还要估计细胞对其环境和整体组织的影响。\n", "\n", "Celcomen试图使用因果可识别的GenAI框架来处理这个问题，该框架由两部分组成：\n", "\n", "1. **推理模块（CCE - Cell Communication Energy）**：使用空间转录组学数据学习基因-基因作用力的值（直到其马尔可夫等价类），并将它们解耦为细胞内和细胞间成分\n", "2. **生成模块（SCE - Spatial Counterfactual Engine）**：使用学习到的基因-基因作用力来预测空间反事实（如基因敲除和细胞注射）对组织的影响\n", "\n", "## 数据要求\n", "\n", "所需的输入是存储为anndata格式的原始基因计数数据。请从[10x_human_glioblastoma](https://www.10xgenomics.com/datasets/ffpe-human-brain-cancer-data-with-human-immuno-oncology-profiling-panel-and-custom-add-on-1-standard)下载cell_feature_matrix和cells.csv.gz文件，并将它们解压到与此notebook相同目录下的\"data\"文件夹中，然后再继续。\n", "\n", "- 原始基因计数数据（未经标准化或对数变换）\n", "- 空间坐标信息存储在`adata.obsm[\"spatial\"]`中\n", "- AnnData格式的数据对象"]}, {"cell_type": "code", "execution_count": null, "id": "import-cell", "metadata": {"tags": []}, "outputs": [], "source": ["# 导入相关包\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "import scanpy as sc\n", "import scipy.stats as ss\n", "import seaborn as sns\n", "from tqdm import tqdm\n", "\n", "import torch\n", "import torch_geometric\n", "from torch_geometric.loader import DataLoader\n", "\n", "# 导入celcomen模块\n", "from celcomen.datareaders.datareader import get_dataset_loaders\n", "from celcomen.models.celcomen import celcomen\n", "from celcomen.models.simcomen import simcomen\n", "from celcomen.training_plan.train import train\n", "from celcomen.utils.helpers import calc_gex, normalize_g2g, calc_sphex\n", "\n", "# 设置图形参数\n", "sc.settings.set_figure_params(dpi=100)\n", "\n", "# 设置随机种子以确保结果可重现\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "\n", "print(\"所有包导入成功！\")\n", "print(f\"PyTorch版本: {torch.__version__}\")\n", "print(f\"PyTorch Geometric版本: {torch_geometric.__version__}\")\n", "print(f\"Scanpy版本: {sc.__version__}\")"]}, {"cell_type": "markdown", "id": "preprocessing-title", "metadata": {}, "source": ["# 1. 数据预处理\n", "\n", "在这一部分，我们将加载和预处理人类胶质母细胞瘤的空间转录组学数据。"]}, {"cell_type": "code", "execution_count": null, "id": "load-data", "metadata": {"tags": []}, "outputs": [], "source": ["# 加载数据\n", "# 注意：请确保您已经下载了数据并放在正确的目录中\n", "data_path = \"data/xenium_human_glioblastoma.h5ad\"  # 根据实际数据路径调整\n", "\n", "try:\n", "    # 读取AnnData对象\n", "    adata = sc.read_h5ad(data_path)\n", "    print(f\"数据加载成功！\")\n", "    print(f\"数据形状: {adata.shape}\")\n", "    print(f\"细胞数量: {adata.n_obs}\")\n", "    print(f\"基因数量: {adata.n_vars}\")\n", "except FileNotFoundError:\n", "    print(f\"未找到数据文件: {data_path}\")\n", "    print(\"请确保您已经下载了数据并放在正确的目录中\")\n", "    # 创建示例数据用于演示\n", "    print(\"创建示例数据用于演示...\")\n", "    n_cells = 1000\n", "    n_genes = 500\n", "    \n", "    # 生成随机基因表达数据\n", "    X = np.random.poisson(5, size=(n_cells, n_genes))\n", "    \n", "    # 生成空间坐标\n", "    spatial_coords = np.random.uniform(0, 100, size=(n_cells, 2))\n", "    \n", "    # 创建AnnData对象\n", "    adata = sc.AnnData(X=X)\n", "    adata.obsm['spatial'] = spatial_coords\n", "    adata.obs['sample_id'] = 'sample_1'\n", "    \n", "    # 添加基因名称\n", "    adata.var_names = [f'Gene_{i}' for i in range(n_genes)]\n", "    adata.obs_names = [f'Cell_{i}' for i in range(n_cells)]\n", "    \n", "    print(f\"示例数据创建完成！\")\n", "    print(f\"数据形状: {adata.shape}\")"]}, {"cell_type": "code", "execution_count": null, "id": "explore-data", "metadata": {"tags": []}, "outputs": [], "source": ["# 数据探索\n", "print(\"=== 数据基本信息 ===\")\n", "print(f\"观测值（细胞）数量: {adata.n_obs}\")\n", "print(f\"变量（基因）数量: {adata.n_vars}\")\n", "print(f\"数据类型: {type(adata.X)}\")\n", "\n", "# 检查空间坐标\n", "if 'spatial' in adata.obsm.keys():\n", "    print(f\"空间坐标形状: {adata.obsm['spatial'].shape}\")\n", "    print(f\"空间坐标范围: X轴 [{adata.obsm['spatial'][:, 0].min():.2f}, {adata.obsm['spatial'][:, 0].max():.2f}]\")\n", "    print(f\"                Y轴 [{adata.obsm['spatial'][:, 1].min():.2f}, {adata.obsm['spatial'][:, 1].max():.2f}]\")\n", "else:\n", "    print(\"警告：未找到空间坐标信息\")\n", "\n", "# 检查样本ID\n", "if 'sample_id' in adata.obs.columns:\n", "    print(f\"样本数量: {adata.obs['sample_id'].nunique()}\")\n", "    print(f\"样本ID: {adata.obs['sample_id'].unique()}\")\n", "else:\n", "    print(\"添加样本ID...\")\n", "    adata.obs['sample_id'] = 'sample_1'\n", "\n", "# 基因表达统计\n", "print(\"\\n=== 基因表达统计 ===\")\n", "print(f\"总计数: {adata.X.sum():.0f}\")\n", "print(f\"每个细胞的平均基因数: {(adata.X > 0).sum(axis=1).mean():.1f}\")\n", "print(f\"每个基因的平均细胞数: {(adata.X > 0).sum(axis=0).mean():.1f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "visualize-spatial", "metadata": {"tags": []}, "outputs": [], "source": ["# 可视化空间分布\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# 空间坐标散点图\n", "axes[0].scatter(adata.obsm['spatial'][:, 0], adata.obsm['spatial'][:, 1], \n", "                alpha=0.6, s=1, c='blue')\n", "axes[0].set_xlabel('X坐标')\n", "axes[0].set_ylabel('Y坐标')\n", "axes[0].set_title('细胞空间分布')\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "# 总基因表达量的空间分布\n", "total_counts = np.array(adata.X.sum(axis=1)).flatten()\n", "scatter = axes[1].scatter(adata.obsm['spatial'][:, 0], adata.obsm['spatial'][:, 1], \n", "                         c=total_counts, alpha=0.6, s=1, cmap='viridis')\n", "axes[1].set_xlabel('X坐标')\n", "axes[1].set_ylabel('Y坐标')\n", "axes[1].set_title('总基因表达量的空间分布')\n", "plt.colorbar(scatter, ax=axes[1], label='总计数')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"空间分布可视化完成\")"]}, {"cell_type": "markdown", "id": "filtering-title", "metadata": {}, "source": ["## 1.1 数据过滤和质量控制\n", "\n", "在训练模型之前，我们需要对数据进行质量控制和过滤。"]}, {"cell_type": "code", "execution_count": null, "id": "quality-control", "metadata": {"tags": []}, "outputs": [], "source": ["# 数据质量控制\n", "print(\"=== 数据质量控制 ===\")\n", "\n", "# 计算质量指标\n", "# 每个细胞的基因数\n", "adata.obs['n_genes'] = (adata.X > 0).sum(axis=1)\n", "# 每个细胞的总计数\n", "adata.obs['total_counts'] = np.array(adata.X.sum(axis=1)).flatten()\n", "# 每个基因的细胞数\n", "adata.var['n_cells'] = (adata.X > 0).sum(axis=0)\n", "\n", "print(f\"过滤前 - 细胞数: {adata.n_obs}, 基因数: {adata.n_vars}\")\n", "\n", "# 过滤低质量细胞\n", "# 过滤表达基因数太少的细胞\n", "min_genes = 50\n", "sc.pp.filter_cells(adata, min_genes=min_genes)\n", "print(f\"过滤表达基因数<{min_genes}的细胞后 - 细胞数: {adata.n_obs}\")\n", "\n", "# 过滤在太少细胞中表达的基因\n", "min_cells = 10\n", "sc.pp.filter_genes(adata, min_cells=min_cells)\n", "print(f\"过滤在<{min_cells}个细胞中表达的基因后 - 基因数: {adata.n_vars}\")\n", "\n", "# 选择高变基因（可选）\n", "if adata.n_vars > 2000:\n", "    print(\"选择高变基因...\")\n", "    # 计算高变基因\n", "    sc.pp.highly_variable_genes(adata, n_top_genes=2000, flavor='seurat_v3')\n", "    # 保留高变基因\n", "    adata = adata[:, adata.var.highly_variable]\n", "    print(f\"选择高变基因后 - 基因数: {adata.n_vars}\")\n", "\n", "print(f\"\\n最终数据形状: {adata.shape}\")\n", "print(f\"数据类型: {adata.X.dtype}\")\n", "\n", "# 确保数据是整数类型（原始计数）\n", "if not np.issubdtype(adata.X.dtype, np.integer):\n", "    print(\"将数据转换为整数类型...\")\n", "    adata.X = adata.X.astype(np.int32)\n", "\n", "print(\"数据质量控制完成！\")"]}, {"cell_type": "markdown", "id": "dataloader-title", "metadata": {}, "source": ["# 2. 创建数据加载器\n", "\n", "使用Celcomen的数据读取器创建PyTorch Geometric数据加载器。"]}, {"cell_type": "code", "execution_count": null, "id": "create-dataloader", "metadata": {"tags": []}, "outputs": [], "source": ["# 保存处理后的数据\n", "processed_data_path = \"processed_data.h5ad\"\n", "adata.write(processed_data_path)\n", "print(f\"处理后的数据已保存到: {processed_data_path}\")\n", "\n", "# 设置参数\n", "n_neighbors = 6  # 空间邻居数量（适用于Visium数据）\n", "distance = 50.0  # 邻居距离阈值\n", "device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "print(f\"使用设备: {device}\")\n", "\n", "# 创建数据加载器\n", "print(\"创建数据加载器...\")\n", "try:\n", "    loader = get_dataset_loaders(\n", "        h5ad_path=processed_data_path,\n", "        sample_id_name='sample_id',\n", "        n_neighbors=n_neighbors,\n", "        distance=distance,\n", "        device=device,\n", "        verbose=True\n", "    )\n", "    print(\"数据加载器创建成功！\")\n", "    print(f\"批次数量: {len(loader)}\")\n", "    \n", "    # 检查第一个批次\n", "    for batch in loader:\n", "        print(f\"批次形状: {batch.x.shape}\")\n", "        print(f\"边索引形状: {batch.edge_index.shape}\")\n", "        print(f\"批次索引形状: {batch.batch.shape}\")\n", "        break\n", "        \n", "except Exception as e:\n", "    print(f\"创建数据加载器时出错: {e}\")\n", "    print(\"使用简化的数据加载器...\")\n", "    \n", "    # 创建简化的数据加载器\n", "    from sklearn.neighbors import kneighbors_graph\n", "    import torch_geometric.data as data\n", "    \n", "    # 构建k近邻图\n", "    spatial_coords = adata.obsm['spatial']\n", "    knn_graph = kneighbors_graph(spatial_coords, n_neighbors=n_neighbors, mode='connectivity')\n", "    \n", "    # 转换为PyTorch Geometric格式\n", "    edge_index = torch.tensor(np.array(knn_graph.nonzero()), dtype=torch.long)\n", "    x = torch.tensor(adata.X.toarray() if hasattr(adata.X, 'toarray') else adata.X, dtype=torch.float32)\n", "    \n", "    # 创建数据对象\n", "    graph_data = data.Data(x=x, edge_index=edge_index)\n", "    loader = data.DataLoader([graph_data], batch_size=1, shuffle=False)\n", "    \n", "    print(\"简化数据加载器创建成功！\")"]}, {"cell_type": "markdown", "id": "model-title", "metadata": {}, "source": ["# 3. 模型初始化和训练\n", "\n", "初始化Celcomen模型并进行训练。"]}, {"cell_type": "code", "execution_count": null, "id": "initialize-model", "metadata": {"tags": []}, "outputs": [], "source": ["# 模型参数\n", "input_dim = adata.n_vars  # 输入维度（基因数量）\n", "output_dim = min(100, input_dim // 2)  # 输出维度\n", "learning_rate = 0.01\n", "epochs = 100\n", "zmft_scalar = 1e-1  # 平均场理论标量\n", "seed = 42\n", "\n", "print(f\"模型参数:\")\n", "print(f\"  输入维度: {input_dim}\")\n", "print(f\"  输出维度: {output_dim}\")\n", "print(f\"  学习率: {learning_rate}\")\n", "print(f\"  训练轮数: {epochs}\")\n", "print(f\"  设备: {device}\")\n", "\n", "# 初始化模型\n", "model = celcomen(\n", "    input_dim=input_dim,\n", "    output_dim=output_dim,\n", "    n_neighbors=n_neighbors,\n", "    seed=seed\n", ").to(device)\n", "\n", "print(f\"\\n模型初始化完成！\")\n", "print(f\"模型参数数量: {sum(p.numel() for p in model.parameters())}\")\n", "print(f\"可训练参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad)}\")\n", "\n", "# 显示模型结构\n", "print(f\"\\n模型结构:\")\n", "print(model)"]}, {"cell_type": "code", "execution_count": null, "id": "train-model", "metadata": {"tags": []}, "outputs": [], "source": ["# 训练模型\n", "print(\"开始训练模型...\")\n", "print(f\"训练设备: {device}\")\n", "\n", "try:\n", "    # 训练模型\n", "    losses = train(\n", "        epochs=epochs,\n", "        learning_rate=learning_rate,\n", "        model=model,\n", "        loader=loader,\n", "        zmft_scalar=zmft_scalar,\n", "        seed=seed,\n", "        device=device,\n", "        verbose=True\n", "    )\n", "    \n", "    print(f\"\\n训练完成！\")\n", "    print(f\"最终损失: {losses[-1]:.6f}\")\n", "    \n", "    # 绘制训练损失曲线\n", "    plt.figure(figsize=(10, 6))\n", "    plt.plot(losses)\n", "    plt.xlabel('训练轮数')\n", "    plt.ylabel('损失值')\n", "    plt.title('训练损失曲线')\n", "    plt.grid(True, alpha=0.3)\n", "    plt.show()\n", "    \n", "except Exception as e:\n", "    print(f\"训练过程中出错: {e}\")\n", "    print(\"尝试简化训练...\")\n", "    \n", "    # 简化训练过程\n", "    optimizer = torch.optim.SGD(model.parameters(), lr=learning_rate)\n", "    model.train()\n", "    \n", "    losses = []\n", "    for epoch in tqdm(range(epochs)):\n", "        epoch_losses = []\n", "        for batch in loader:\n", "            batch = batch.to(device)\n", "            optimizer.zero_grad()\n", "            \n", "            # 前向传播\n", "            try:\n", "                msg, msg_intra, log_z_mft = model(batch.edge_index, batch.batch)\n", "                \n", "                # 计算损失\n", "                loss = -log_z_mft.mean()  # 简化的损失函数\n", "                \n", "                # 反向传播\n", "                loss.backward()\n", "                optimizer.step()\n", "                \n", "                epoch_losses.append(loss.item())\n", "            except Exception as batch_error:\n", "                print(f\"批次处理错误: {batch_error}\")\n", "                continue\n", "        \n", "        if epoch_losses:\n", "            avg_loss = np.mean(epoch_losses)\n", "            losses.append(avg_loss)\n", "            \n", "            if epoch % 10 == 0:\n", "                print(f\"轮数 {epoch}, 损失: {avg_loss:.6f}\")\n", "    \n", "    print(\"简化训练完成！\")"]}, {"cell_type": "markdown", "id": "analysis-title", "metadata": {}, "source": ["# 4. 模型分析和结果解释\n", "\n", "分析训练好的模型，提取基因间相互作用网络。"]}, {"cell_type": "code", "execution_count": null, "id": "extract-networks", "metadata": {"tags": []}, "outputs": [], "source": ["# 提取学习到的网络\n", "print(\"提取学习到的基因网络...\")\n", "\n", "model.eval()\n", "with torch.no_grad():\n", "    # 提取基因间相互作用矩阵（G2G）\n", "    g2g_matrix = model.conv1.lin.weight.detach().cpu().numpy()\n", "    print(f\"基因间相互作用矩阵形状: {g2g_matrix.shape}\")\n", "    \n", "    # 提取细胞内调控矩阵\n", "    intra_matrix = model.lin.weight.detach().cpu().numpy()\n", "    print(f\"细胞内调控矩阵形状: {intra_matrix.shape}\")\n", "\n", "# 可视化网络\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# 基因间相互作用热图\n", "im1 = axes[0].imshow(g2g_matrix, cmap='RdBu_r', aspect='auto')\n", "axes[0].set_title('基因间相互作用矩阵 (G2G)')\n", "axes[0].set_xlabel('输入基因')\n", "axes[0].set_ylabel('输出特征')\n", "plt.colorbar(im1, ax=axes[0])\n", "\n", "# 细胞内调控热图\n", "im2 = axes[1].imshow(intra_matrix, cmap='RdBu_r', aspect='auto')\n", "axes[1].set_title('细胞内调控矩阵')\n", "axes[1].set_xlabel('输入基因')\n", "axes[1].set_ylabel('输出特征')\n", "plt.colorbar(im2, ax=axes[1])\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 网络统计\n", "print(f\"\\n网络统计:\")\n", "print(f\"G2G矩阵 - 平均值: {g2g_matrix.mean():.6f}, 标准差: {g2g_matrix.std():.6f}\")\n", "print(f\"细胞内矩阵 - 平均值: {intra_matrix.mean():.6f}, 标准差: {intra_matrix.std():.6f}\")"]}, {"cell_type": "markdown", "id": "counterfactual-title", "metadata": {}, "source": ["# 5. 空间反事实分析\n", "\n", "使用训练好的模型进行空间反事实分析，模拟基因敲除的效果。"]}, {"cell_type": "code", "execution_count": null, "id": "initialize-simcomen", "metadata": {"tags": []}, "outputs": [], "source": ["# 初始化SimComen模型用于反事实分析\n", "print(\"初始化SimComen模型用于反事实分析...\")\n", "\n", "# 创建SimComen模型\n", "sim_model = simcomen(\n", "    input_dim=input_dim,\n", "    output_dim=output_dim,\n", "    n_neighbors=n_neighbors,\n", "    seed=seed\n", ").to(device)\n", "\n", "# 将训练好的权重转移到SimComen模型\n", "sim_model.conv1.lin.weight = model.conv1.lin.weight\n", "sim_model.conv1.bias = model.conv1.bias\n", "sim_model.lin.weight = model.lin.weight\n", "sim_model.lin.bias = model.lin.bias\n", "\n", "print(\"SimComen模型初始化完成！\")\n", "\n", "# 设置原始基因表达数据\n", "original_gex = torch.tensor(adata.X.toarray() if hasattr(adata.X, 'toarray') else adata.X, \n", "                           dtype=torch.float32).to(device)\n", "\n", "print(f\"原始基因表达数据形状: {original_gex.shape}\")\n", "print(f\"基因表达范围: [{original_gex.min():.2f}, {original_gex.max():.2f}]\")"]}, {"cell_type": "code", "execution_count": null, "id": "gene-knockout", "metadata": {"tags": []}, "outputs": [], "source": ["# 基因敲除模拟\n", "print(\"进行基因敲除模拟...\")\n", "\n", "# 选择要敲除的基因（选择表达量较高的基因）\n", "gene_expression_mean = original_gex.mean(dim=0)\n", "top_genes_idx = torch.argsort(gene_expression_mean, descending=True)[:10]\n", "target_gene_idx = top_genes_idx[0].item()  # 选择表达量最高的基因\n", "\n", "print(f\"选择敲除基因索引: {target_gene_idx}\")\n", "print(f\"该基因的平均表达量: {gene_expression_mean[target_gene_idx]:.2f}\")\n", "\n", "# 创建敲除后的基因表达数据\n", "knockout_gex = original_gex.clone()\n", "knockout_gex[:, target_gene_idx] = 0  # 将目标基因表达设为0\n", "\n", "print(f\"敲除后基因表达数据创建完成\")\n", "print(f\"敲除基因的表达量: {knockout_gex[:, target_gene_idx].sum():.2f}\")\n", "\n", "# 计算表达差异\n", "expression_diff = knockout_gex - original_gex\n", "print(f\"表达差异统计:\")\n", "print(f\"  平均差异: {expression_diff.mean():.6f}\")\n", "print(f\"  最大差异: {expression_diff.max():.6f}\")\n", "print(f\"  最小差异: {expression_diff.min():.6f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "predict-effects", "metadata": {"tags": []}, "outputs": [], "source": ["# 预测敲除效果\n", "print(\"预测基因敲除的空间效果...\")\n", "\n", "sim_model.eval()\n", "with torch.no_grad():\n", "    try:\n", "        # 设置原始和敲除后的基因表达\n", "        sim_model.set_gex(original_gex)\n", "        \n", "        # 获取第一个批次进行预测\n", "        for batch in loader:\n", "            batch = batch.to(device)\n", "            \n", "            # 原始预测\n", "            original_msg, original_msg_intra, original_log_z = sim_model(batch.edge_index, batch.batch)\n", "            \n", "            # 敲除后预测\n", "            sim_model.set_gex(knockout_gex)\n", "            knockout_msg, knockout_msg_intra, knockout_log_z = sim_model(batch.edge_index, batch.batch)\n", "            \n", "            break  # 只处理第一个批次\n", "        \n", "        # 计算预测差异\n", "        msg_diff = knockout_msg - original_msg\n", "        msg_intra_diff = knockout_msg_intra - original_msg_intra\n", "        log_z_diff = knockout_log_z - original_log_z\n", "        \n", "        print(\"预测完成！\")\n", "        print(f\"细胞间信息差异统计:\")\n", "        print(f\"  平均差异: {msg_diff.mean():.6f}\")\n", "        print(f\"  标准差: {msg_diff.std():.6f}\")\n", "        \n", "        print(f\"细胞内信息差异统计:\")\n", "        print(f\"  平均差异: {msg_intra_diff.mean():.6f}\")\n", "        print(f\"  标准差: {msg_intra_diff.std():.6f}\")\n", "        \n", "        print(f\"配分函数差异: {log_z_diff.mean():.6f}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"预测过程中出错: {e}\")\n", "        print(\"使用简化预测方法...\")\n", "        \n", "        # 简化预测：直接比较基因表达\n", "        msg_diff = knockout_gex - original_gex\n", "        print(f\"简化预测完成，表达差异范围: [{msg_diff.min():.2f}, {msg_diff.max():.2f}]\")"]}, {"cell_type": "code", "execution_count": null, "id": "visualize-effects", "metadata": {"tags": []}, "outputs": [], "source": ["# 可视化敲除效果\n", "print(\"可视化基因敲除的空间效果...\")\n", "\n", "# 计算每个细胞的总效应\n", "if 'msg_diff' in locals():\n", "    if msg_diff.dim() > 1:\n", "        total_effect = msg_diff.sum(dim=1).cpu().numpy()\n", "    else:\n", "        total_effect = msg_diff.cpu().numpy()\n", "else:\n", "    total_effect = expression_diff.sum(dim=1).cpu().numpy()\n", "\n", "# 创建可视化\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# 原始基因表达的空间分布\n", "original_target_expr = original_gex[:, target_gene_idx].cpu().numpy()\n", "scatter1 = axes[0, 0].scatter(adata.obsm['spatial'][:, 0], adata.obsm['spatial'][:, 1], \n", "                             c=original_target_expr, alpha=0.6, s=1, cmap='viridis')\n", "axes[0, 0].set_title(f'原始基因表达 (基因 {target_gene_idx})')\n", "axes[0, 0].set_xlabel('X坐标')\n", "axes[0, 0].set_ylabel('Y坐标')\n", "plt.colorbar(scatter1, ax=axes[0, 0])\n", "\n", "# 敲除后基因表达的空间分布\n", "knockout_target_expr = knockout_gex[:, target_gene_idx].cpu().numpy()\n", "scatter2 = axes[0, 1].scatter(adata.obsm['spatial'][:, 0], adata.obsm['spatial'][:, 1], \n", "                             c=knockout_target_expr, alpha=0.6, s=1, cmap='viridis')\n", "axes[0, 1].set_title(f'敲除后基因表达 (基因 {target_gene_idx})')\n", "axes[0, 1].set_xlabel('X坐标')\n", "axes[0, 1].set_ylabel('Y坐标')\n", "plt.colorbar(scatter2, ax=axes[0, 1])\n", "\n", "# 总效应的空间分布\n", "scatter3 = axes[1, 0].scatter(adata.obsm['spatial'][:, 0], adata.obsm['spatial'][:, 1], \n", "                             c=total_effect, alpha=0.6, s=1, cmap='RdBu_r')\n", "axes[1, 0].set_title('基因敲除的总效应')\n", "axes[1, 0].set_xlabel('X坐标')\n", "axes[1, 0].set_ylabel('Y坐标')\n", "plt.colorbar(scatter3, ax=axes[1, 0])\n", "\n", "# 效应分布直方图\n", "axes[1, 1].hist(total_effect, bins=50, alpha=0.7, edgecolor='black')\n", "axes[1, 1].set_title('总效应分布')\n", "axes[1, 1].set_xlabel('效应大小')\n", "axes[1, 1].set_ylabel('细胞数量')\n", "axes[1, 1].axvline(x=0, color='red', linestyle='--', alpha=0.7, label='无效应')\n", "axes[1, 1].legend()\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"可视化完成！\")\n", "print(f\"受影响的细胞数量: {np.sum(np.abs(total_effect) > 0.1)}\")\n", "print(f\"平均效应大小: {np.mean(np.abs(total_effect)):.6f}\")"]}, {"cell_type": "markdown", "id": "analysis-summary-title", "metadata": {}, "source": ["# 6. 结果分析和总结\n", "\n", "分析模型结果并总结发现。"]}, {"cell_type": "code", "execution_count": null, "id": "analyze-results", "metadata": {"tags": []}, "outputs": [], "source": ["# 分析基因网络\n", "print(\"=== 基因网络分析 ===\")\n", "\n", "# 分析基因间相互作用强度\n", "g2g_abs = np.abs(g2g_matrix)\n", "strong_interactions = g2g_abs > np.percentile(g2g_abs, 95)\n", "print(f\"强相互作用数量: {strong_interactions.sum()}\")\n", "print(f\"强相互作用比例: {strong_interactions.sum() / g2g_abs.size * 100:.2f}%\")\n", "\n", "# 分析细胞内调控\n", "intra_abs = np.abs(intra_matrix)\n", "strong_intra = intra_abs > np.percentile(intra_abs, 95)\n", "print(f\"强细胞内调控数量: {strong_intra.sum()}\")\n", "print(f\"强细胞内调控比例: {strong_intra.sum() / intra_abs.size * 100:.2f}%\")\n", "\n", "# 网络连通性分析\n", "print(f\"\\n=== 网络连通性 ===\")\n", "print(f\"G2G矩阵非零元素比例: {(g2g_abs > 1e-6).sum() / g2g_abs.size * 100:.2f}%\")\n", "print(f\"细胞内矩阵非零元素比例: {(intra_abs > 1e-6).sum() / intra_abs.size * 100:.2f}%\")\n", "\n", "# 基因重要性排序\n", "gene_importance = g2g_abs.sum(axis=0) + intra_abs.sum(axis=0)\n", "top_genes = np.argsort(gene_importance)[-10:][::-1]\n", "\n", "print(f\"\\n=== 重要基因排序（前10） ===\")\n", "for i, gene_idx in enumerate(top_genes):\n", "    gene_name = adata.var_names[gene_idx] if hasattr(adata, 'var_names') else f\"Gene_{gene_idx}\"\n", "    print(f\"{i+1:2d}. {gene_name} (索引: {gene_idx}, 重要性: {gene_importance[gene_idx]:.4f})\")"]}, {"cell_type": "code", "execution_count": null, "id": "spatial-analysis", "metadata": {"tags": []}, "outputs": [], "source": ["# 空间效应分析\n", "print(\"=== 空间效应分析 ===\")\n", "\n", "# 计算空间自相关性\n", "from scipy.spatial.distance import pdist, squareform\n", "from scipy.stats import pearsonr\n", "\n", "# 计算空间距离矩阵\n", "spatial_coords = adata.obsm['spatial']\n", "spatial_dist = squareform(pdist(spatial_coords))\n", "\n", "# 计算效应的空间自相关\n", "if len(total_effect) > 1:\n", "    # 选择距离阈值\n", "    distance_threshold = np.percentile(spatial_dist[spatial_dist > 0], 10)\n", "    \n", "    # 找到邻近细胞对\n", "    neighbor_pairs = np.where((spatial_dist > 0) & (spatial_dist < distance_threshold))\n", "    \n", "    if len(neighbor_pairs[0]) > 0:\n", "        # 计算邻近细胞间效应的相关性\n", "        neighbor_effects_1 = total_effect[neighbor_pairs[0]]\n", "        neighbor_effects_2 = total_effect[neighbor_pairs[1]]\n", "        \n", "        if len(neighbor_effects_1) > 1:\n", "            spatial_corr, p_value = pearsonr(neighbor_effects_1, neighbor_effects_2)\n", "            print(f\"空间自相关系数: {spatial_corr:.4f} (p值: {p_value:.4f})\")\n", "        else:\n", "            print(\"邻近细胞对数量不足，无法计算空间自相关\")\n", "    else:\n", "        print(\"未找到邻近细胞对\")\n", "else:\n", "    print(\"效应数据不足，无法进行空间分析\")\n", "\n", "# 效应强度分布分析\n", "print(f\"\\n=== 效应强度分析 ===\")\n", "effect_abs = np.abs(total_effect)\n", "print(f\"效应强度统计:\")\n", "print(f\"  平均值: {effect_abs.mean():.6f}\")\n", "print(f\"  中位数: {np.median(effect_abs):.6f}\")\n", "print(f\"  标准差: {effect_abs.std():.6f}\")\n", "print(f\"  最大值: {effect_abs.max():.6f}\")\n", "\n", "# 效应方向分析\n", "positive_effects = np.sum(total_effect > 0)\n", "negative_effects = np.sum(total_effect < 0)\n", "no_effects = np.sum(np.abs(total_effect) < 1e-6)\n", "\n", "print(f\"\\n=== 效应方向分析 ===\")\n", "print(f\"正效应细胞数: {positive_effects} ({positive_effects/len(total_effect)*100:.1f}%)\")\n", "print(f\"负效应细胞数: {negative_effects} ({negative_effects/len(total_effect)*100:.1f}%)\")\n", "print(f\"无效应细胞数: {no_effects} ({no_effects/len(total_effect)*100:.1f}%)\")"]}, {"cell_type": "code", "execution_count": null, "id": "save-results", "metadata": {"tags": []}, "outputs": [], "source": ["# 保存结果\n", "print(\"=== 保存分析结果 ===\")\n", "\n", "# 保存模型\n", "model_save_path = \"celcomen_model.pth\"\n", "torch.save({\n", "    'model_state_dict': model.state_dict(),\n", "    'model_params': {\n", "        'input_dim': input_dim,\n", "        'output_dim': output_dim,\n", "        'n_neighbors': n_neighbors,\n", "        'seed': seed\n", "    },\n", "    'training_params': {\n", "        'learning_rate': learning_rate,\n", "        'epochs': epochs,\n", "        'zmft_scalar': zmft_scalar\n", "    },\n", "    'losses': losses if 'losses' in locals() else []\n", "}, model_save_path)\n", "print(f\"模型已保存到: {model_save_path}\")\n", "\n", "# 保存网络矩阵\n", "np.save(\"g2g_matrix.npy\", g2g_matrix)\n", "np.save(\"intra_matrix.npy\", intra_matrix)\n", "print(\"网络矩阵已保存\")\n", "\n", "# 保存效应结果\n", "results_dict = {\n", "    'target_gene_idx': target_gene_idx,\n", "    'total_effect': total_effect,\n", "    'spatial_coords': spatial_coords,\n", "    'gene_importance': gene_importance,\n", "    'top_genes': top_genes\n", "}\n", "np.save(\"knockout_results.npy\", results_dict)\n", "print(\"敲除结果已保存\")\n", "\n", "# 创建结果摘要\n", "summary = f\"\"\"\n", "=== Celcomen 分析结果摘要 ===\n", "\n", "数据信息:\n", "- 细胞数量: {adata.n_obs}\n", "- 基因数量: {adata.n_vars}\n", "- 空间坐标范围: X[{spatial_coords[:, 0].min():.1f}, {spatial_coords[:, 0].max():.1f}], Y[{spatial_coords[:, 1].min():.1f}, {spatial_coords[:, 1].max():.1f}]\n", "\n", "模型参数:\n", "- 输入维度: {input_dim}\n", "- 输出维度: {output_dim}\n", "- 邻居数量: {n_neighbors}\n", "- 训练轮数: {epochs}\n", "- 学习率: {learning_rate}\n", "\n", "网络分析:\n", "- 强基因间相互作用: {strong_interactions.sum()} ({strong_interactions.sum() / g2g_abs.size * 100:.2f}%)\n", "- 强细胞内调控: {strong_intra.sum()} ({strong_intra.sum() / intra_abs.size * 100:.2f}%)\n", "\n", "基因敲除分析:\n", "- 敲除基因索引: {target_gene_idx}\n", "- 受影响细胞数: {np.sum(np.abs(total_effect) > 0.1)}\n", "- 平均效应强度: {np.mean(np.abs(total_effect)):.6f}\n", "- 正效应细胞: {positive_effects} ({positive_effects/len(total_effect)*100:.1f}%)\n", "- 负效应细胞: {negative_effects} ({negative_effects/len(total_effect)*100:.1f}%)\n", "\n", "文件输出:\n", "- 模型文件: {model_save_path}\n", "- 网络矩阵: g2g_matrix.npy, intra_matrix.npy\n", "- 敲除结果: knockout_results.npy\n", "\"\"\"\n", "\n", "print(summary)\n", "\n", "# 保存摘要到文件\n", "with open(\"analysis_summary.txt\", \"w\", encoding=\"utf-8\") as f:\n", "    f.write(summary)\n", "\n", "print(\"\\n分析完成！所有结果已保存。\")"]}, {"cell_type": "markdown", "id": "conclusion-title", "metadata": {}, "source": ["# 7. 结论和后续步骤\n", "\n", "## 主要发现\n", "\n", "1. **模型训练**: 成功训练了Celcomen模型，学习了基因间和细胞内的调控网络\n", "2. **网络解耦**: 模型能够将基因调控网络分解为细胞间通讯和细胞内调控两个组分\n", "3. **反事实预测**: 通过SimComen模块成功模拟了基因敲除的空间效应\n", "4. **空间模式**: 发现基因敲除效应在空间上呈现特定的分布模式\n", "\n", "## 模型优势\n", "\n", "- **因果可识别性**: 具有理论保证的因果推理能力\n", "- **空间感知**: 能够捕捉细胞间的空间相互作用\n", "- **反事实生成**: 可以预测各种扰动的效果\n", "- **可解释性**: 提供可解释的基因调控网络\n", "\n", "## 后续研究方向\n", "\n", "1. **多基因敲除**: 研究多个基因同时敲除的协同效应\n", "2. **细胞注入**: 模拟在特定位置注入不同类型细胞的效果\n", "3. **药物响应**: 预测药物治疗对组织的空间效应\n", "4. **疾病建模**: 应用于疾病进展和治疗响应的建模\n", "5. **跨物种分析**: 在不同物种的数据上验证模型的泛化能力\n", "\n", "## 技术改进\n", "\n", "1. **模型架构**: 探索更复杂的图神经网络架构\n", "2. **训练策略**: 优化训练算法和超参数\n", "3. **数据增强**: 开发适用于空间转录组学的数据增强方法\n", "4. **计算效率**: 提高大规模数据处理的计算效率\n", "\n", "---\n", "\n", "**注意**: 本分析使用的是示例数据或简化的真实数据。在实际应用中，请确保使用完整的、高质量的空间转录组学数据，并根据具体的研究问题调整模型参数和分析流程。"]}], "metadata": {"kernelspec": {"display_name": "pytorch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.13"}}, "nbformat": 4, "nbformat_minor": 5}