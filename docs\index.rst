.. Celcomen documentation master file, created by
   sphinx-quickstart on Thu Jan  9 18:35:31 2025.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.

Celcomen
========
Project home page `here <https://github.com/Teichlab/celcomen>`_.

Main workflow classes
-----------------------

The ``celcomen`` class
----------------------

.. autoclass:: celcomen.models.celcomen.celcomen
    :members:
    :undoc-members:
    :show-inheritance:

The ``simcomen`` class
----------------------

.. autoclass:: celcomen.models.simcomen.simcomen
    :members:
    :undoc-members:
    :show-inheritance:
      
Utility functions
-----------------
.. autosummary::
   :toctree:
   
   celcomen.utils.helpers.calc_gex
   celcomen.datareaders.datareader.get_dataset_loaders

