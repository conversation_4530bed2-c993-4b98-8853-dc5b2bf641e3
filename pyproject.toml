[build-system]
requires = ["flit_core >=3.2,<4"]
build-backend = "flit_core.buildapi"

[project]
name = "celcomen"
authors = [{name = "Stathis Megas", email = "<EMAIL>"}]
version = "0.0.2"
description = "Celcomen is a first step towards models of Virtual Tissue, which generalize models of Virtual Cells to the tissue context, and can be leveraged to predict tissue counterfactuals such as spatially localised gene knockouts"
dependencies = [
    "numpy",
    "torch",
    "torch_geometric",
    "scikit-learn",
    "scanpy",
    "tqdm"
]
