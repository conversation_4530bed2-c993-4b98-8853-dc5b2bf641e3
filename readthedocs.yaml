# .readthedocs.yaml
# Read the Docs configuration file
# See https://docs.readthedocs.io/en/stable/config-file/v2.html for details

# Required
version: 2

# Set the version of Python and other tools you might need
build:
  os: ubuntu-20.04
  tools:
    python: "3.10"

# Build documentation in the docs/ directory with Sphinx
sphinx:
   configuration: docs/conf.py

# Install our python package before building the docs
python:
  install:
    - requirements: docs/requirements.txt
    - method: pip
      path: .
